{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "FinTranzo - 金融交易追蹤系統", "homepage": "https://19930913.xyz", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:web": "expo export --platform web", "predeploy": "npm run build:web", "deploy": "gh-pages -d dist", "fetch-stocks": "tsx scripts/fetch-taiwan-stocks.ts", "import-csv": "tsx scripts/import-csv-stocks.ts", "test-supabase": "node test_supabase_connection.js", "update-stocks": "node -r ts-node/register scripts/fetch-taiwan-stocks.ts", "build-scripts": "tsc scripts/*.ts --outDir dist/scripts", "update:tw": "node scripts/update-taiwan-stocks.js", "update:us": "node scripts/update-us-stocks.js", "update:rates": "node scripts/update-exchange-rates.js", "test:github": "node test-github-actions-simple.js", "test:local": "node test-local-updates.js", "test:apis": "node -e \"require('./test-local-updates.js')\"", "test:simple": "node scripts/simple-function-test.js", "test:verify": "node scripts/verify-core-functions.js", "monitor": "node scripts/monitor.js", "docker:web": "docker-compose up fintranzo-web", "docker:ios": "docker-compose up fintranzo-ios-simulator", "docker:prod": "docker-compose -f docker-compose.production.yml up", "docker:test": "bash scripts/docker-comprehensive-test.sh", "docker:build": "docker-compose build", "docker:clean": "docker system prune -a", "k8s:deploy": "kubectl apply -f k8s/", "k8s:test": "kubectl apply -f k8s/test-job.yaml", "k8s:validate": "kubectl apply -f k8s/validation-job.yaml", "k8s:logs": "kubectl logs -f deployment/fintranzo-app -n fintranzo", "test:five-core": "node scripts/comprehensive-five-functions-test.js", "test:real-env": "node scripts/real-environment-10-rounds-test.js", "test:sync": "node scripts/comprehensive-sync-test.js", "test:delete": "node scripts/test-new-delete-system.js", "test:delete:web": "PLATFORM=web node scripts/test-new-delete-system.js", "test:delete:ios": "PLATFORM=ios node scripts/test-new-delete-system.js", "docker:test:delete": "docker-compose -f docker/docker-compose.test.yml up --build"}, "dependencies": {"@babel/plugin-transform-runtime": "^7.27.3", "@craftzdog/react-native-buffer": "^6.1.0", "@expo/config-plugins": "~10.0.0", "@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^7.3.13", "@react-navigation/native": "^7.1.9", "@react-navigation/stack": "^7.3.2", "@supabase/supabase-js": "^2.39.0", "assert": "^2.1.0", "axios": "^1.6.0", "babel-plugin-module-resolver": "^5.0.2", "base-64": "^1.0.0", "buffer": "^6.0.3", "cross-env": "^7.0.3", "csv-parse": "^5.6.0", "events": "^3.3.0", "expo": "53.0.16", "expo-auth-session": "~6.2.0", "expo-build-properties": "~0.14.8", "expo-crypto": "~14.1.5", "expo-dev-client": "~5.2.3", "expo-haptics": "~14.1.4", "expo-linking": "~7.1.6", "expo-sensors": "~14.1.1", "expo-status-bar": "~2.2.3", "expo-web-browser": "~14.2.0", "node-fetch": "^2.6.7", "path-browserify": "^1.0.1", "querystring-es3": "^0.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-native": "^0.73.6", "react-native-calendars": "^1.1306.0", "react-native-dotenv": "^3.4.11", "react-native-draggable-flatlist": "^4.0.3", "react-native-gesture-handler": "~2.24.0", "react-native-get-random-values": "^1.11.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "readable-stream": "^4.7.0", "util": "^0.12.5", "zustand": "^5.0.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/node": "^22.15.24", "@types/react": "~19.0.10", "dotenv": "^16.5.0", "express": "^5.1.0", "ts-node": "^10.9.0", "tsx": "^4.0.0", "typescript": "~5.8.3"}, "private": true, "engines": {"node": ">=20.0.0"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"listUnknownPackages": false}}}}