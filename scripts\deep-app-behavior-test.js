/**
 * 深度應用行為測試
 * 模擬真實應用的所有操作流程
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Supabase 配置
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ 缺少 Supabase 環境變量');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

// 測試帳號
const TEST_EMAIL = '<EMAIL>';
const TEST_PASSWORD = 'user01';

// UUID 生成函數
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

async function loginUser() {
  console.log('🔐 登錄測試帳號:', TEST_EMAIL);
  const { data, error } = await supabase.auth.signInWithPassword({
    email: TEST_EMAIL,
    password: TEST_PASSWORD,
  });

  if (error) {
    console.error('❌ 登錄失敗:', error.message);
    return null;
  }

  console.log('✅ 登錄成功! 用戶 ID:', data.user.id);
  return data.user;
}

async function testRealTransactionAdd(user) {
  console.log('\n📝 測試真實新增交易流程...');
  
  try {
    // 模擬 AddTransactionModal 的完整流程
    console.log('📝 步驟1: 模擬用戶填寫交易表單...');
    
    // 模擬用戶輸入
    const userInput = {
      amount: '150',
      type: 'expense',
      description: '午餐',
      category: '餐飲',
      account: '現金',
      date: new Date().toISOString()
    };

    console.log('📝 用戶輸入:', userInput);

    // 模擬 AddTransactionModal 的 handleSubmit 邏輯
    console.log('📝 步驟2: 模擬 AddTransactionModal.handleSubmit...');
    
    // 模擬 ensureValidUUID 邏輯
    const transactionId = generateUUID(); // 這裡應該使用 ensureValidUUID(editingTransaction?.id)
    
    const transaction = {
      id: transactionId,
      amount: parseFloat(userInput.amount),
      type: userInput.type,
      description: userInput.description,
      category: userInput.category,
      account: userInput.account,
      date: userInput.date,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    console.log('📝 生成的交易對象:', {
      id: transaction.id,
      amount: transaction.amount,
      description: transaction.description,
      category: transaction.category
    });

    // 模擬 TransactionsScreen 的 handleAddTransaction 邏輯
    console.log('📝 步驟3: 模擬 TransactionsScreen.handleAddTransaction...');
    
    // 檢查 ID 格式
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    const isValidUUID = uuidRegex.test(transaction.id);
    
    if (!isValidUUID) {
      console.error('❌ 生成的交易 ID 格式無效:', transaction.id);
      return false;
    }

    // 模擬 transactionDataService.addTransaction
    console.log('📝 步驟4: 模擬 transactionDataService.addTransaction...');
    
    // 先檢查是否存在，然後插入或更新（模擬修復後的邏輯）
    const { data: existingTransaction } = await supabase
      .from('transactions')
      .select('id')
      .eq('id', transaction.id)
      .eq('user_id', user.id)
      .single();

    let error;
    if (existingTransaction) {
      // 更新現有交易
      const { error: updateError } = await supabase
        .from('transactions')
        .update({
          amount: transaction.amount,
          type: transaction.type,
          description: transaction.description,
          category: transaction.category,
          account: transaction.account,
          date: transaction.date,
          updated_at: transaction.updated_at
        })
        .eq('id', transaction.id)
        .eq('user_id', user.id);
      error = updateError;
    } else {
      // 插入新交易
      const { error: insertError } = await supabase
        .from('transactions')
        .insert({
          ...transaction,
          user_id: user.id
        });
      error = insertError;
    }

    if (error) {
      console.error('❌ 真實新增交易失敗:', error.message);
      return false;
    }

    console.log('✅ 真實新增交易成功');

    // 清理測試數據
    await supabase.from('transactions').delete().eq('id', transaction.id);
    
    return true;

  } catch (error) {
    console.error('❌ 測試真實新增交易異常:', error.message);
    return false;
  }
}

async function testRealAssetTransactionSync(user) {
  console.log('\n📝 測試真實資產與交易連動...');
  
  try {
    // 創建測試資產
    const testAsset = {
      id: generateUUID(),
      user_id: user.id,
      name: '測試現金帳戶',
      type: '現金',
      value: 5000,
      current_value: 5000,
      cost_basis: 5000,
      quantity: 1,
      sort_order: 0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    console.log('📝 步驟1: 創建測試資產...');
    const { error: assetError } = await supabase
      .from('assets')
      .insert(testAsset);

    if (assetError) {
      console.error('❌ 創建測試資產失敗:', assetError.message);
      return false;
    }

    // 創建影響該資產的交易
    const testTransaction = {
      id: generateUUID(),
      user_id: user.id,
      amount: 200,
      type: 'expense',
      description: '測試支出',
      category: '餐飲',
      account: '測試現金帳戶',
      date: new Date().toISOString().split('T')[0],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    console.log('📝 步驟2: 創建影響資產的交易...');
    const { error: transactionError } = await supabase
      .from('transactions')
      .insert(testTransaction);

    if (transactionError) {
      console.error('❌ 創建測試交易失敗:', transactionError.message);
      return false;
    }

    // 模擬 assetTransactionSyncService.processTransaction 邏輯
    console.log('📝 步驟3: 模擬資產金額更新...');
    
    // 計算新的資產金額
    const newAssetValue = testAsset.current_value - testTransaction.amount;
    
    // 更新資產
    const { error: updateAssetError } = await supabase
      .from('assets')
      .update({
        current_value: newAssetValue,
        updated_at: new Date().toISOString()
      })
      .eq('id', testAsset.id)
      .eq('user_id', user.id);

    if (updateAssetError) {
      console.error('❌ 更新資產失敗:', updateAssetError.message);
      return false;
    }

    // 驗證資產金額是否正確更新
    const { data: updatedAsset } = await supabase
      .from('assets')
      .select('current_value')
      .eq('id', testAsset.id)
      .single();

    if (updatedAsset && updatedAsset.current_value === newAssetValue) {
      console.log('✅ 資產與交易連動正常');
      console.log(`📊 資產金額變化: ${testAsset.current_value} -> ${updatedAsset.current_value}`);
      
      // 清理測試數據
      await supabase.from('transactions').delete().eq('id', testTransaction.id);
      await supabase.from('assets').delete().eq('id', testAsset.id);
      
      return true;
    } else {
      console.log('❌ 資產與交易連動失敗');
      
      // 清理測試數據
      await supabase.from('transactions').delete().eq('id', testTransaction.id);
      await supabase.from('assets').delete().eq('id', testAsset.id);
      
      return false;
    }

  } catch (error) {
    console.error('❌ 測試真實資產交易連動異常:', error.message);
    return false;
  }
}

async function testRealCategoryPreservation(user) {
  console.log('\n📝 測試真實垃圾桶刪除不影響類別...');
  
  try {
    // 獲取當前類別數量
    const { data: initialCategories } = await supabase
      .from('categories')
      .select('*')
      .eq('user_id', user.id);

    const initialCategoryCount = initialCategories ? initialCategories.length : 0;
    console.log(`📊 初始類別數量: ${initialCategoryCount}`);

    // 創建一些測試交易
    const testTransactions = [
      {
        id: generateUUID(),
        user_id: user.id,
        amount: 100,
        type: 'expense',
        description: '測試交易1',
        category: '餐飲',
        account: '現金',
        date: new Date().toISOString().split('T')[0],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: generateUUID(),
        user_id: user.id,
        amount: 200,
        type: 'income',
        description: '測試交易2',
        category: '薪水',
        account: '銀行',
        date: new Date().toISOString().split('T')[0],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];

    console.log('📝 步驟1: 創建測試交易...');
    const { error: transactionError } = await supabase
      .from('transactions')
      .insert(testTransactions);

    if (transactionError) {
      console.error('❌ 創建測試交易失敗:', transactionError.message);
      return false;
    }

    // 檢查交易創建後的狀態
    const { data: beforeDeleteTransactions } = await supabase
      .from('transactions')
      .select('*')
      .eq('user_id', user.id);

    const { data: beforeDeleteCategories } = await supabase
      .from('categories')
      .select('*')
      .eq('user_id', user.id);

    console.log(`📊 刪除前: ${beforeDeleteTransactions.length} 筆交易, ${beforeDeleteCategories.length} 個類別`);

    // 模擬垃圾桶刪除全部交易（模擬 DashboardScreen 的邏輯）
    console.log('📝 步驟2: 模擬垃圾桶刪除全部交易...');
    
    // 只刪除交易，不刪除類別
    const { error: deleteError } = await supabase
      .from('transactions')
      .delete()
      .eq('user_id', user.id);

    if (deleteError) {
      console.error('❌ 刪除交易失敗:', deleteError.message);
      return false;
    }

    // 檢查刪除後的狀態
    const { data: afterDeleteTransactions } = await supabase
      .from('transactions')
      .select('*')
      .eq('user_id', user.id);

    const { data: afterDeleteCategories } = await supabase
      .from('categories')
      .select('*')
      .eq('user_id', user.id);

    console.log(`📊 刪除後: ${afterDeleteTransactions.length} 筆交易, ${afterDeleteCategories.length} 個類別`);

    // 驗證結果
    if (afterDeleteTransactions.length === 0 && afterDeleteCategories.length === beforeDeleteCategories.length) {
      console.log('✅ 垃圾桶刪除不影響類別功能正常');
      return true;
    } else {
      console.log('❌ 垃圾桶刪除影響了類別');
      console.log(`預期類別數量: ${beforeDeleteCategories.length}, 實際類別數量: ${afterDeleteCategories.length}`);
      return false;
    }

  } catch (error) {
    console.error('❌ 測試真實類別保護異常:', error.message);
    return false;
  }
}

async function testRealCloudSync(user) {
  console.log('\n📝 測試真實雲端同步...');
  
  try {
    // 測試資產同步
    console.log('📝 步驟1: 測試資產雲端同步...');
    const testAsset = {
      id: generateUUID(),
      user_id: user.id,
      name: '雲端同步測試資產',
      type: '投資',
      value: 8000,
      current_value: 8000,
      cost_basis: 8000,
      quantity: 1,
      sort_order: 0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { error: assetSyncError } = await supabase
      .from('assets')
      .insert(testAsset);

    if (assetSyncError) {
      console.error('❌ 資產雲端同步失敗:', assetSyncError.message);
      return false;
    }

    // 測試交易同步
    console.log('📝 步驟2: 測試交易雲端同步...');
    const testTransaction = {
      id: generateUUID(),
      user_id: user.id,
      amount: 300,
      type: 'income',
      description: '雲端同步測試交易',
      category: '薪水',
      account: '銀行',
      date: new Date().toISOString().split('T')[0],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { error: transactionSyncError } = await supabase
      .from('transactions')
      .insert(testTransaction);

    if (transactionSyncError) {
      console.error('❌ 交易雲端同步失敗:', transactionSyncError.message);
      return false;
    }

    console.log('✅ 雲端同步功能正常');

    // 清理測試數據
    await supabase.from('assets').delete().eq('id', testAsset.id);
    await supabase.from('transactions').delete().eq('id', testTransaction.id);

    return true;

  } catch (error) {
    console.error('❌ 測試真實雲端同步異常:', error.message);
    return false;
  }
}

async function main() {
  console.log('🚀 開始深度應用行為測試...');
  console.log('================================');
  console.log('📝 測試真實應用的完整流程：');
  console.log('1. 真實新增交易流程');
  console.log('2. 真實資產與交易連動');
  console.log('3. 真實垃圾桶刪除不影響類別');
  console.log('4. 真實雲端同步');
  console.log('================================');
  
  // 登錄用戶
  const user = await loginUser();
  if (!user) {
    console.log('❌ 登錄失敗，測試終止');
    return false;
  }

  // 執行所有測試
  const test1 = await testRealTransactionAdd(user);
  const test2 = await testRealAssetTransactionSync(user);
  const test3 = await testRealCategoryPreservation(user);
  const test4 = await testRealCloudSync(user);

  console.log('\n🎯 深度應用行為測試結果');
  console.log('================================');
  
  console.log('📝 真實應用流程測試:');
  console.log(`  1. 真實新增交易流程: ${test1 ? '✅ 正常' : '❌ 異常'}`);
  console.log(`  2. 真實資產與交易連動: ${test2 ? '✅ 正常' : '❌ 異常'}`);
  console.log(`  3. 真實垃圾桶刪除不影響類別: ${test3 ? '✅ 正常' : '❌ 異常'}`);
  console.log(`  4. 真實雲端同步: ${test4 ? '✅ 正常' : '❌ 異常'}`);

  const allPassed = test1 && test2 && test3 && test4;

  console.log('\n🏆 最終結果:');
  if (allPassed) {
    console.log('🎉 所有真實應用流程測試都通過！');
    console.log('✅ 本地功能完全正常');
    console.log('✅ 雲端功能完全正常');
    console.log('✅ 應用已準備好投入使用');
  } else {
    console.log('⚠️ 部分真實應用流程測試失敗：');
    if (!test1) console.log('  - 真實新增交易流程有問題');
    if (!test2) console.log('  - 真實資產與交易連動有問題');
    if (!test3) console.log('  - 真實垃圾桶刪除影響類別');
    if (!test4) console.log('  - 真實雲端同步有問題');
    console.log('❌ 應用需要進一步修復');
  }

  // 登出用戶
  await supabase.auth.signOut();
  console.log('\n👋 測試完成，用戶已登出');

  return allPassed;
}

main().catch(console.error);
