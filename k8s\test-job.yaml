apiVersion: batch/v1
kind: Job
metadata:
  name: fintranzo-test-job
  labels:
    app: fintranzo-test
spec:
  template:
    metadata:
      labels:
        app: fintranzo-test
    spec:
      restartPolicy: Never
      containers:
      - name: fintranzo-test
        image: fintranzo:latest
        command: ["/bin/bash"]
        args: ["/app/scripts/docker-test-runner.sh"]
        env:
        - name: NODE_ENV
          value: "test"
        - name: EXPO_PUBLIC_SUPABASE_URL
          value: "https://yrryyapzkgrsahranzvo.supabase.co"
        - name: EXPO_PUBLIC_SUPABASE_ANON_KEY
          value: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlycnl5YXB6a2dyc2FocmFuenZvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgxNzM2MzUsImV4cCI6MjA2Mzc0OTYzNX0.TccJJ9KGG6R4KiaDb-548kRkhTaPMODYa6vlQsj8dmM"
        - name: TEST_USER_EMAIL
          value: "<EMAIL>"
        - name: TEST_USER_PASSWORD
          value: "user01"
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        volumeMounts:
        - name: test-results
          mountPath: /app/test-results
      volumes:
      - name: test-results
        emptyDir: {}
  backoffLimit: 3
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: test-config
data:
  test-user-email: "<EMAIL>"
  test-user-password: "user01"
  test-iterations: "10"
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: fintranzo-scheduled-test
spec:
  schedule: "0 */6 * * *"  # 每6小時運行一次
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            app: fintranzo-scheduled-test
        spec:
          restartPolicy: OnFailure
          containers:
          - name: fintranzo-test
            image: fintranzo:latest
            command: ["/bin/bash"]
            args: ["/app/scripts/docker-test-runner.sh"]
            env:
            - name: NODE_ENV
              value: "test"
            - name: EXPO_PUBLIC_SUPABASE_URL
              value: "https://yrryyapzkgrsahranzvo.supabase.co"
            - name: EXPO_PUBLIC_SUPABASE_ANON_KEY
              valueFrom:
                secretKeyRef:
                  name: supabase-secret
                  key: anon-key
            - name: TEST_ITERATIONS
              valueFrom:
                configMapKeyRef:
                  name: test-config
                  key: test-iterations
            resources:
              requests:
                memory: "256Mi"
                cpu: "250m"
              limits:
                memory: "512Mi"
                cpu: "500m"
---
apiVersion: v1
kind: Service
metadata:
  name: fintranzo-test-service
spec:
  selector:
    app: fintranzo-test
  ports:
    - name: http
      protocol: TCP
      port: 80
      targetPort: 3000
  type: ClusterIP
