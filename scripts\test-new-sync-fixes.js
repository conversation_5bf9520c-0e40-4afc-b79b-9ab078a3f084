/**
 * 測試新的同步修復效果
 * 模擬應用層面的操作來測試同步功能
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Supabase 配置
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ 缺少 Supabase 環境變量');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

// 測試帳號
const TEST_EMAIL = '<EMAIL>';
const TEST_PASSWORD = 'user01';

// UUID 生成函數
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

async function loginUser() {
  console.log('🔐 登錄測試帳號:', TEST_EMAIL);
  const { data, error } = await supabase.auth.signInWithPassword({
    email: TEST_EMAIL,
    password: TEST_PASSWORD,
  });

  if (error) {
    console.error('❌ 登錄失敗:', error.message);
    return null;
  }

  console.log('✅ 登錄成功! 用戶 ID:', data.user.id);
  return data.user;
}

// 模擬修復後的交易新增同步邏輯
async function simulateFixedTransactionAdd(user, transaction) {
  console.log('📝 模擬修復後的交易新增同步邏輯...');
  
  try {
    // 準備 Supabase 格式的數據（按照修復後的邏輯）
    const supabaseTransaction = {
      id: transaction.id,
      user_id: user.id,
      account_id: null,
      amount: transaction.amount || 0,
      type: transaction.type,
      description: transaction.description || '',
      category: transaction.category || '',
      account: transaction.account || '',
      from_account: transaction.fromAccount || null,
      to_account: transaction.toAccount || null,
      date: transaction.date || new Date().toISOString().split('T')[0],
      is_recurring: transaction.is_recurring || false,
      recurring_frequency: transaction.recurring_frequency || null,
      max_occurrences: transaction.max_occurrences || null,
      start_date: transaction.start_date || null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    console.log('📝 準備同步的交易數據:', {
      id: supabaseTransaction.id,
      description: supabaseTransaction.description,
      amount: supabaseTransaction.amount,
      type: supabaseTransaction.type
    });

    // 使用 upsert 插入或更新交易記錄
    const { error: upsertError } = await supabase
      .from('transactions')
      .upsert(supabaseTransaction, {
        onConflict: 'id',
        ignoreDuplicates: false
      });

    if (upsertError) {
      console.error('❌ 交易同步失敗:', upsertError.message);
      return false;
    } else {
      console.log('✅ 交易同步成功:', transaction.id);
      return true;
    }

  } catch (error) {
    console.error('❌ 交易同步異常:', error.message);
    return false;
  }
}

// 模擬修復後的資產新增同步邏輯
async function simulateFixedAssetAdd(user, asset) {
  console.log('📝 模擬修復後的資產新增同步邏輯...');
  
  try {
    // 準備 Supabase 格式的數據（按照修復後的邏輯）
    const supabaseAsset = {
      id: asset.id,
      user_id: user.id,
      name: asset.name || '未命名資產',
      type: asset.type || 'other',
      value: Number(asset.current_value || asset.cost_basis || 0),
      current_value: Number(asset.current_value || asset.cost_basis || 0),
      cost_basis: Number(asset.cost_basis || asset.current_value || 0),
      quantity: Number(asset.quantity || 1),
      stock_code: asset.stock_code,
      purchase_price: Number(asset.purchase_price || asset.cost_basis || 0),
      current_price: Number(asset.current_price || asset.current_value || asset.cost_basis || 0),
      sort_order: asset.sort_order || 0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    console.log('📝 準備同步的資產數據:', {
      id: supabaseAsset.id,
      name: supabaseAsset.name,
      type: supabaseAsset.type,
      current_value: supabaseAsset.current_value
    });

    // 使用 upsert 插入或更新資產記錄
    const { error: upsertError } = await supabase
      .from('assets')
      .upsert(supabaseAsset, {
        onConflict: 'id',
        ignoreDuplicates: false
      });

    if (upsertError) {
      console.error('❌ 資產同步失敗:', upsertError.message);
      return false;
    } else {
      console.log('✅ 資產同步成功:', asset.id);
      return true;
    }

  } catch (error) {
    console.error('❌ 資產同步異常:', error.message);
    return false;
  }
}

async function testTransactionAddFix(user) {
  console.log('\n📝 測試交易新增同步修復...');
  
  try {
    // 記錄測試前的交易數量
    const { data: beforeTransactions, error: beforeError } = await supabase
      .from('transactions')
      .select('*')
      .eq('user_id', user.id);

    if (beforeError) {
      console.error('❌ 查詢測試前交易失敗:', beforeError.message);
      return false;
    }

    const beforeCount = beforeTransactions.length;
    console.log(`📊 測試前交易數量: ${beforeCount}`);

    // 創建測試交易
    const testTransaction = {
      id: generateUUID(),
      amount: 500,
      type: 'income',
      description: '測試修復後新增同步',
      category: '薪水',
      account: '銀行',
      date: new Date().toISOString().split('T')[0]
    };

    console.log('📝 創建測試交易:', testTransaction.description);

    // 模擬修復後的同步邏輯
    const syncResult = await simulateFixedTransactionAdd(user, testTransaction);
    
    if (!syncResult) {
      console.log('❌ 交易同步邏輯失敗');
      return false;
    }

    // 等待一秒確保數據已保存
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 驗證交易是否正確保存
    const { data: afterTransactions, error: afterError } = await supabase
      .from('transactions')
      .select('*')
      .eq('user_id', user.id);

    if (afterError) {
      console.error('❌ 查詢測試後交易失敗:', afterError.message);
      return false;
    }

    const afterCount = afterTransactions.length;
    console.log(`📊 測試後交易數量: ${afterCount}`);

    // 檢查新增的交易
    const newTransaction = afterTransactions.find(t => t.id === testTransaction.id);
    
    if (afterCount === beforeCount + 1 && newTransaction) {
      console.log('✅ 交易新增同步修復成功');
      console.log('📝 新增交易詳情:', {
        id: newTransaction.id,
        description: newTransaction.description,
        amount: newTransaction.amount,
        category: newTransaction.category
      });
      
      // 清理測試數據
      await supabase
        .from('transactions')
        .delete()
        .eq('id', testTransaction.id)
        .eq('user_id', user.id);
      
      return true;
    } else {
      console.log('❌ 交易新增同步修復失敗');
      if (afterCount !== beforeCount + 1) {
        console.log(`  數量不匹配: 期望 ${beforeCount + 1}, 實際 ${afterCount}`);
      }
      if (!newTransaction) {
        console.log('  找不到新增的交易記錄');
      }
      return false;
    }

  } catch (error) {
    console.error('❌ 測試交易新增同步修復異常:', error.message);
    return false;
  }
}

async function testAssetAddFix(user) {
  console.log('\n📝 測試資產新增同步修復...');
  
  try {
    // 記錄測試前的資產數量
    const { data: beforeAssets, error: beforeError } = await supabase
      .from('assets')
      .select('*')
      .eq('user_id', user.id);

    if (beforeError) {
      console.error('❌ 查詢測試前資產失敗:', beforeError.message);
      return false;
    }

    const beforeCount = beforeAssets.length;
    console.log(`📊 測試前資產數量: ${beforeCount}`);

    // 創建測試資產
    const testAsset = {
      id: generateUUID(),
      name: '測試修復後新增同步資產',
      type: '投資',
      current_value: 12000,
      cost_basis: 12000,
      quantity: 1,
      sort_order: 0
    };

    console.log('📝 創建測試資產:', testAsset.name);

    // 模擬修復後的同步邏輯
    const syncResult = await simulateFixedAssetAdd(user, testAsset);
    
    if (!syncResult) {
      console.log('❌ 資產同步邏輯失敗');
      return false;
    }

    // 等待一秒確保數據已保存
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 驗證資產是否正確保存
    const { data: afterAssets, error: afterError } = await supabase
      .from('assets')
      .select('*')
      .eq('user_id', user.id);

    if (afterError) {
      console.error('❌ 查詢測試後資產失敗:', afterError.message);
      return false;
    }

    const afterCount = afterAssets.length;
    console.log(`📊 測試後資產數量: ${afterCount}`);

    // 檢查新增的資產
    const newAsset = afterAssets.find(a => a.id === testAsset.id);
    
    if (afterCount === beforeCount + 1 && newAsset) {
      console.log('✅ 資產新增同步修復成功');
      console.log('📝 新增資產詳情:', {
        id: newAsset.id,
        name: newAsset.name,
        type: newAsset.type,
        current_value: newAsset.current_value
      });
      
      // 清理測試數據
      await supabase
        .from('assets')
        .delete()
        .eq('id', testAsset.id)
        .eq('user_id', user.id);
      
      return true;
    } else {
      console.log('❌ 資產新增同步修復失敗');
      if (afterCount !== beforeCount + 1) {
        console.log(`  數量不匹配: 期望 ${beforeCount + 1}, 實際 ${afterCount}`);
      }
      if (!newAsset) {
        console.log('  找不到新增的資產記錄');
      }
      return false;
    }

  } catch (error) {
    console.error('❌ 測試資產新增同步修復異常:', error.message);
    return false;
  }
}

async function main() {
  console.log('🚀 開始測試新的同步修復效果...');
  console.log('================================');
  
  // 1. 登錄用戶
  const user = await loginUser();
  if (!user) {
    console.log('❌ 登錄失敗，測試終止');
    return false;
  }

  // 2. 測試交易新增同步修復
  const transactionResult = await testTransactionAddFix(user);

  // 3. 測試資產新增同步修復
  const assetResult = await testAssetAddFix(user);

  console.log('\n🎯 修復測試結果總結');
  console.log('================================');
  
  console.log('📝 新增同步修復測試:');
  console.log(`  交易新增同步: ${transactionResult ? '✅ 修復成功' : '❌ 仍有問題'}`);
  console.log(`  資產新增同步: ${assetResult ? '✅ 修復成功' : '❌ 仍有問題'}`);

  const allFixed = transactionResult && assetResult;

  console.log('\n📊 整體修復結果:');
  if (allFixed) {
    console.log('🎉 所有同步問題都已修復！');
    console.log('✅ 新增的交易會自動同步到雲端');
    console.log('✅ 新增的資產會自動同步到雲端');
    console.log('✅ 刪除操作也會正確同步到雲端');
  } else {
    console.log('⚠️ 部分同步問題仍需處理：');
    if (!transactionResult) console.log('  - 交易新增同步仍有問題');
    if (!assetResult) console.log('  - 資產新增同步仍有問題');
  }

  // 4. 登出用戶
  await supabase.auth.signOut();
  console.log('\n👋 測試完成，用戶已登出');

  return allFixed;
}

main().catch(console.error);
