apiVersion: apps/v1
kind: Deployment
metadata:
  name: fintranzo-app
  labels:
    app: fintranzo
spec:
  replicas: 3
  selector:
    matchLabels:
      app: fintranzo
  template:
    metadata:
      labels:
        app: fintranzo
    spec:
      containers:
      - name: fintranzo
        image: fintranzo:latest
        ports:
        - containerPort: 3000
        - containerPort: 8081
        env:
        - name: NODE_ENV
          value: "production"
        - name: EXPO_PUBLIC_SUPABASE_URL
          value: "https://yrryyapzkgrsahranzvo.supabase.co"
        - name: EXPO_PUBLIC_SUPABASE_ANON_KEY
          value: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.TccJJ9KGG6R4KiaDb-548kRkhTaPMODYa6vlQsj8dmM"
        - name: TEST_USER_EMAIL
          value: "<EMAIL>"
        - name: TEST_USER_PASSWORD
          value: "user01"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: fintranzo-service
spec:
  selector:
    app: fintranzo
  ports:
    - name: web
      protocol: TCP
      port: 80
      targetPort: 3000
    - name: expo
      protocol: TCP
      port: 8081
      targetPort: 8081
  type: LoadBalancer
---
apiVersion: v1
kind: Secret
metadata:
  name: supabase-secret
type: Opaque
data:
  anon-key: ZXlKaGJHY2lPaUpJVXpJMU5pSXNJblI1Y0NJNklrcFhWQ0o5LmV5SnBjM01pT2lKemRYQmhZbUZ6WlNJc0luSmxaaUk2SW5seWNubDVZWEI2YTJkeWMyRm9jbUZ1ZW5adklpd2ljbTlzWlNJNkltRnViMjRpTENKcFlYUWlPakUzTkRneE56TTJNRFV6TlN3aVpYaHdJam94TURBMk16YzBPVFl6TlgwLlRjY0pKOUtHRzZSNEtpYURiLTU0OGtSa2hUYVBNT0RZYTZ2bFFzajhkbU0=
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: fintranzo-ingress
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - 19930913.xyz
    secretName: fintranzo-tls
  rules:
  - host: 19930913.xyz
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: fintranzo-service
            port:
              number: 80
