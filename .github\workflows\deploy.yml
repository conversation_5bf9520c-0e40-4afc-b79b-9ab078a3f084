name: Deploy to GitHub Pages

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]

# 添加權限設定
permissions:
  contents: read
  pages: write
  id-token: write

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        
    - name: Install dependencies
      run: npm install --legacy-peer-deps
      
    - name: Build for web
      run: npm run build:web
      env:
        EXPO_PUBLIC_SUPABASE_URL: ${{ secrets.EXPO_PUBLIC_SUPABASE_URL }}
        EXPO_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.EXPO_PUBLIC_SUPABASE_ANON_KEY }}
        EXPO_PUBLIC_REDIRECT_URL: https://19930913.xyz
        EXPO_PUBLIC_WEB_URL: https://19930913.xyz
        NODE_ENV: production

    - name: Add CNAME file
      run: echo "19930913.xyz" > ./dist/CNAME
        
    - name: Setup Pages
      uses: actions/configure-pages@v4

    - name: Upload artifact
      uses: actions/upload-pages-artifact@v3
      with:
        path: ./dist

    - name: Deploy to GitHub Pages
      id: deployment
      uses: actions/deploy-pages@v4
      if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'
