{"expo": {"name": "FinTranzo", "slug": "FinTranzo", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": false, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.hovertw.fintranzo2025", "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "NSAppTransportSecurity": {"NSAllowsArbitraryLoads": true}}, "buildNumber": "1"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.hovertw.fintranzo", "versionCode": 1, "permissions": ["INTERNET", "ACCESS_NETWORK_STATE"]}, "web": {"favicon": "./assets/favicon.png", "bundler": "metro", "output": "single"}, "extra": {"eas": {"projectId": "6c2ae12e-bf69-4c90-905e-bbd39a5a7553"}}, "scheme": "<PERSON><PERSON><PERSON><PERSON>", "plugins": [["expo-build-properties", {"ios": {"newArchEnabled": false}, "android": {"newArchEnabled": false}}], "expo-web-browser"]}}