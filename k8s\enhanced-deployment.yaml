apiVersion: v1
kind: ConfigMap
metadata:
  name: fintranzo-enhanced-config
  namespace: fintranzo
data:
  NODE_ENV: "production"
  EXPO_PUBLIC_PLATFORM: "web"
  EXPO_PUBLIC_WEB_URL: "https://19930913.xyz"
  EXPO_PUBLIC_REDIRECT_URL: "https://19930913.xyz"
  CONNECTION_TIMEOUT: "30000"
  RETRY_ATTEMPTS: "5"
  HEARTBEAT_INTERVAL: "30000"
  MAX_RECONNECT_ATTEMPTS: "10"
  RECONNECT_DELAY: "2000"
---
apiVersion: v1
kind: Secret
metadata:
  name: fintranzo-enhanced-secrets
  namespace: fintranzo
type: Opaque
data:
  # Base64 encoded Supabase credentials
  EXPO_PUBLIC_SUPABASE_URL: aHR0cHM6Ly95cnJ5eWFwemtncnNhaHJhbnp2by5zdXBhYmFzZS5jbw==
  EXPO_PUBLIC_SUPABASE_ANON_KEY: ZXlKaGJHY2lPaUpJVXpJMU5pSXNJblI1Y0NJNklrcFhWQ0o5LmV5SnBjM01pT2lKemRYQmhZbUZ6WlNJc0luSmxaaUk2SW5seWNubDVZWEI2YTJkeWMyRm9jbUZ1ZW5adklpd2ljbTlzWlNJNkltRnViMjRpTENKcFlYUWlPakUzORRGZ4TkRJeE9EYzBMQ0psZUhBaU9qSXdOVEF3TURBeE9EYzBmUS5UY2NKSjlLR0c2UjRLaWFEYi01NDhrUmtoVGFQTU9EWWE2dmxRc2o4ZG1N
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: fintranzo-web-enhanced
  namespace: fintranzo
  labels:
    app: fintranzo-web-enhanced
    tier: frontend
    version: v2
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 1
  selector:
    matchLabels:
      app: fintranzo-web-enhanced
  template:
    metadata:
      labels:
        app: fintranzo-web-enhanced
        tier: frontend
        version: v2
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "80"
        prometheus.io/path: "/metrics"
    spec:
      containers:
      - name: fintranzo-web
        image: fintranzo/web:latest
        ports:
        - containerPort: 80
          name: http
          protocol: TCP
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: fintranzo-enhanced-config
              key: NODE_ENV
        - name: EXPO_PUBLIC_PLATFORM
          valueFrom:
            configMapKeyRef:
              name: fintranzo-enhanced-config
              key: EXPO_PUBLIC_PLATFORM
        - name: EXPO_PUBLIC_WEB_URL
          valueFrom:
            configMapKeyRef:
              name: fintranzo-enhanced-config
              key: EXPO_PUBLIC_WEB_URL
        - name: EXPO_PUBLIC_SUPABASE_URL
          valueFrom:
            secretKeyRef:
              name: fintranzo-enhanced-secrets
              key: EXPO_PUBLIC_SUPABASE_URL
        - name: EXPO_PUBLIC_SUPABASE_ANON_KEY
          valueFrom:
            secretKeyRef:
              name: fintranzo-enhanced-secrets
              key: EXPO_PUBLIC_SUPABASE_ANON_KEY
        - name: CONNECTION_TIMEOUT
          valueFrom:
            configMapKeyRef:
              name: fintranzo-enhanced-config
              key: CONNECTION_TIMEOUT
        - name: RETRY_ATTEMPTS
          valueFrom:
            configMapKeyRef:
              name: fintranzo-enhanced-config
              key: RETRY_ATTEMPTS
        - name: HEARTBEAT_INTERVAL
          valueFrom:
            configMapKeyRef:
              name: fintranzo-enhanced-config
              key: HEARTBEAT_INTERVAL
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
            httpHeaders:
            - name: X-Health-Check
              value: "liveness"
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
          successThreshold: 1
        readinessProbe:
          httpGet:
            path: /health
            port: 80
            httpHeaders:
            - name: X-Health-Check
              value: "readiness"
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
          successThreshold: 1
        startupProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 30
          successThreshold: 1
        securityContext:
          runAsNonRoot: true
          runAsUser: 1001
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: nginx-cache
          mountPath: /var/cache/nginx
        - name: nginx-run
          mountPath: /var/run
        - name: app-logs
          mountPath: /var/log/app
      volumes:
      - name: tmp
        emptyDir:
          sizeLimit: 100Mi
      - name: nginx-cache
        emptyDir:
          sizeLimit: 200Mi
      - name: nginx-run
        emptyDir:
          sizeLimit: 50Mi
      - name: app-logs
        emptyDir:
          sizeLimit: 100Mi
      securityContext:
        fsGroup: 1001
        runAsGroup: 1001
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - fintranzo-web-enhanced
              topologyKey: kubernetes.io/hostname
      tolerations:
      - key: "node.kubernetes.io/not-ready"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
      - key: "node.kubernetes.io/unreachable"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
---
apiVersion: v1
kind: Service
metadata:
  name: fintranzo-web-enhanced-service
  namespace: fintranzo
  labels:
    app: fintranzo-web-enhanced
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-cross-zone-load-balancing-enabled: "true"
spec:
  type: ClusterIP
  sessionAffinity: ClientIP
  sessionAffinityConfig:
    clientIP:
      timeoutSeconds: 10800
  ports:
  - port: 80
    targetPort: 80
    protocol: TCP
    name: http
  selector:
    app: fintranzo-web-enhanced
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: fintranzo-web-enhanced-ingress
  namespace: fintranzo
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/rate-limit: "200"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "https://19930913.xyz"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - 19930913.xyz
    secretName: fintranzo-enhanced-tls
  rules:
  - host: 19930913.xyz
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: fintranzo-web-enhanced-service
            port:
              number: 80
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: fintranzo-web-enhanced-hpa
  namespace: fintranzo
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: fintranzo-web-enhanced
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 30
      - type: Pods
        value: 2
        periodSeconds: 60
